import React, { useState, useEffect } from 'react';
import { Play, Settings, CheckCircle, AlertCircle, Clock, Zap } from 'lucide-react';

const N8nWorkflowTrigger = () => {
  const [workflows, setWorkflows] = useState({});
  const [selectedWorkflow, setSelectedWorkflow] = useState('');
  const [workflowArgs, setWorkflowArgs] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState(null);
  const [error, setError] = useState(null);

  const API_BASE = import.meta.env.VITE_API_BASE_URL || '/api';

  // Fetch available workflows on component mount
  useEffect(() => {
    const fetchWorkflows = async () => {
      try {
        const response = await fetch(`${API_BASE}/n8n/workflows`);
        const data = await response.json();
        setWorkflows(data.workflows);
      } catch (err) {
        console.error('Failed to fetch workflows:', err);
        setError('Failed to load available workflows');
      }
    };

    fetchWorkflows();
  }, [API_BASE]);

  // Handle workflow selection
  const handleWorkflowChange = (workflowId) => {
    setSelectedWorkflow(workflowId);
    setWorkflowArgs({});
    setResult(null);
    setError(null);
  };

  // Handle argument input changes
  const handleArgChange = (key, value) => {
    setWorkflowArgs(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // Trigger the selected workflow
  const triggerWorkflow = async () => {
    if (!selectedWorkflow) {
      setError('Please select a workflow');
      return;
    }

    setIsLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await fetch(`${API_BASE}/n8n/trigger`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          workflow: selectedWorkflow,
          args: workflowArgs
        })
      });

      const data = await response.json();

      if (response.ok) {
        setResult(data);
      } else {
        setError(data.error || 'Failed to trigger workflow');
      }
    } catch (err) {
      console.error('Error triggering workflow:', err);
      setError('Network error: Failed to trigger workflow');
    } finally {
      setIsLoading(false);
    }
  };

  // Get the current workflow info
  const currentWorkflow = workflows[selectedWorkflow];

  return (
    <div className="bg-gray-900 text-white p-6 rounded-lg shadow-lg max-w-4xl mx-auto">
      <div className="flex items-center space-x-3 mb-6">
        <Zap className="text-yellow-400" size={24} />
        <h2 className="text-2xl font-bold">N8N Workflow Orchestrator</h2>
      </div>

      {/* Workflow Selection */}
      <div className="mb-6">
        <label className="block text-sm font-medium mb-2">Select Workflow</label>
        <select
          value={selectedWorkflow}
          onChange={(e) => handleWorkflowChange(e.target.value)}
          className="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="">Choose a workflow...</option>
          {Object.entries(workflows).map(([id, workflow]) => (
            <option key={id} value={id}>
              {workflow.name}
            </option>
          ))}
        </select>
      </div>

      {/* Workflow Description */}
      {currentWorkflow && (
        <div className="mb-6 p-4 bg-gray-800 rounded-lg">
          <h3 className="text-lg font-semibold mb-2">{currentWorkflow.name}</h3>
          <p className="text-gray-300 mb-3">{currentWorkflow.description}</p>
          
          {/* Show available actions/types if they exist */}
          {currentWorkflow.actions && (
            <div className="mb-2">
              <span className="text-sm font-medium text-blue-400">Available Actions: </span>
              <span className="text-sm text-gray-300">{currentWorkflow.actions.join(', ')}</span>
            </div>
          )}
          {currentWorkflow.types && (
            <div className="mb-2">
              <span className="text-sm font-medium text-green-400">Transaction Types: </span>
              <span className="text-sm text-gray-300">{currentWorkflow.types.join(', ')}</span>
            </div>
          )}
        </div>
      )}

      {/* Dynamic Parameter Inputs */}
      {currentWorkflow && (
        <div className="mb-6">
          <h4 className="text-lg font-semibold mb-3">Parameters</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {currentWorkflow.parameters.map((param) => (
              <div key={param}>
                <label className="block text-sm font-medium mb-1 capitalize">
                  {param.replace(/_/g, ' ')}
                </label>
                <input
                  type="text"
                  value={workflowArgs[param] || ''}
                  onChange={(e) => handleArgChange(param, e.target.value)}
                  placeholder={`Enter ${param.replace(/_/g, ' ')}`}
                  className="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Trigger Button */}
      <div className="mb-6">
        <button
          onClick={triggerWorkflow}
          disabled={!selectedWorkflow || isLoading}
          className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed px-6 py-3 rounded-lg font-semibold transition-colors duration-200"
        >
          {isLoading ? (
            <>
              <Clock className="animate-spin" size={20} />
              <span>Triggering...</span>
            </>
          ) : (
            <>
              <Play size={20} />
              <span>Trigger Workflow</span>
            </>
          )}
        </button>
      </div>

      {/* Error Display */}
      {error && (
        <div className="mb-6 p-4 bg-red-900/50 border border-red-700 rounded-lg flex items-center space-x-2">
          <AlertCircle className="text-red-400" size={20} />
          <span className="text-red-300">{error}</span>
        </div>
      )}

      {/* Result Display */}
      {result && (
        <div className="mb-6 p-4 bg-green-900/50 border border-green-700 rounded-lg">
          <div className="flex items-center space-x-2 mb-3">
            <CheckCircle className="text-green-400" size={20} />
            <span className="text-green-300 font-semibold">Workflow Triggered Successfully</span>
          </div>
          
          <div className="text-sm text-gray-300">
            <div className="mb-2">
              <span className="font-medium">Workflow:</span> {result.workflow}
            </div>
            <div className="mb-2">
              <span className="font-medium">Timestamp:</span> {new Date(result.timestamp).toLocaleString()}
            </div>
            
            {result.result && (
              <div>
                <span className="font-medium">Result:</span>
                <pre className="mt-2 p-3 bg-gray-800 rounded text-xs overflow-x-auto">
                  {JSON.stringify(result.result, null, 2)}
                </pre>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Quick Examples */}
      <div className="mt-8 p-4 bg-gray-800 rounded-lg">
        <h4 className="text-lg font-semibold mb-3 flex items-center space-x-2">
          <Settings size={20} />
          <span>Quick Examples</span>
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <h5 className="font-medium text-blue-400 mb-1">Tasks Workflow</h5>
            <p className="text-gray-300">Create a task: action="create", task_description="Buy groceries"</p>
          </div>
          <div>
            <h5 className="font-medium text-green-400 mb-1">Financial Workflow</h5>
            <p className="text-gray-300">Track expense: transaction_type="expense", amount="50", description="Lunch"</p>
          </div>
          <div>
            <h5 className="font-medium text-purple-400 mb-1">Email Workflow</h5>
            <p className="text-gray-300">Send email: action="send", to="<EMAIL>", subject="Hello"</p>
          </div>
          <div>
            <h5 className="font-medium text-orange-400 mb-1">Receipts Workflow</h5>
            <p className="text-gray-300">Process receipt: action="process", vendor="Starbucks", amount="4.50"</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default N8nWorkflowTrigger;
