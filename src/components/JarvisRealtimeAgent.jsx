import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Mic, MicOff, Send, Bot, Brain, Search, Code, FileText, Phone, PhoneOff } from 'lucide-react';

const JarvisRealtimeAgent = () => {
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [messages, setMessages] = useState([]);
  const [selectedAgent, setSelectedAgent] = useState('general');
  const [selectedVoice, setSelectedVoice] = useState('alloy');
  const [availableAgents, setAvailableAgents] = useState([]);
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const [sessionData, setSessionData] = useState(null);

  const pcRef = useRef(null);
  const dataChannelRef = useRef(null);
  const audioContextRef = useRef(null);
  const microphoneStreamRef = useRef(null);

  const API_BASE = import.meta.env.VITE_REALTIME_API_BASE_URL || '/api';

  const aiAgents = [
    { id: 'general', name: 'General Assistant', icon: Brain, color: 'from-indigo-500 to-purple-500' },
    { id: 'financial', name: 'Financial Assistant', icon: Bot, color: 'from-green-500 to-emerald-500' },
    { id: 'tasks', name: 'Task Organizer', icon: FileText, color: 'from-blue-500 to-cyan-500' },
    { id: 'email', name: 'Email Assistant', icon: Send, color: 'from-purple-500 to-pink-500' },
  ];

  const backgroundElements = useMemo(() => {
    return [...Array(50)].map((_, i) => ({
      id: i,
      width: Math.random() * 200 + 50,
      height: Math.random() * 200 + 50,
      left: Math.random() * 100,
      top: Math.random() * 100,
      animationDelay: Math.random() * 5,
      animationDuration: Math.random() * 10 + 5
    }));
  }, []);

  useEffect(() => {
    const initAudioContext = async () => {
      try {
        audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();
      } catch (error) {
        console.error('Failed to initialize audio context:', error);
      }
    };

    initAudioContext();

    return () => {
      if (audioContextRef.current) {
        audioContextRef.current.close();
      }
    };
  }, []);

  const createRealtimeSession = async () => {
    try {
      setIsConnecting(true);
      setConnectionStatus('connecting');

      const response = await fetch(`${API_BASE}/realtime/session`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ agent: selectedAgent, voice: selectedVoice })
      });

      if (!response.ok) {
        throw new Error(`Failed to create session: ${response.status}`);
      }

      const data = await response.json();
      setSessionData(data);
      setAvailableAgents(data.availableAgents || []);

      return data;
    } catch (error) {
      console.error('Error creating realtime session:', error);
      setConnectionStatus('error');
      throw error;
    }
  };

  const setupWebRTC = async (sessionData) => {
    try {
      const pc = new RTCPeerConnection();
      pcRef.current = pc;

      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      microphoneStreamRef.current = stream;

      stream.getTracks().forEach(track => {
        pc.addTrack(track, stream);
      });

      const dataChannel = pc.createDataChannel('oai-events');
      dataChannelRef.current = dataChannel;

      dataChannel.onopen = () => {
        console.log('Data channel opened');
        setIsConnected(true);
        setConnectionStatus('connected');

        dataChannel.send(JSON.stringify({
          type: 'session.update',
          session: {
            instructions: sessionData.instructions || 'You are JARVIS, a helpful AI assistant.',
            voice: selectedVoice,
            input_audio_format: 'pcm16',
            output_audio_format: 'pcm16',
            input_audio_transcription: {
              model: 'whisper-1'
            },
            turn_detection: {
              type: 'server_vad',
              threshold: 0.5,
              prefix_padding_ms: 300,
              silence_duration_ms: 200
            },
            tools: sessionData.tools || [],
            tool_choice: 'auto',
            temperature: 0.7,
            max_response_output_tokens: 4096
          }
        }));
      };

      dataChannel.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          handleRealtimeEvent(message);
        } catch (error) {
          console.error('Error parsing data channel message:', error);
        }
      };

      dataChannel.onerror = (error) => {
        console.error('Data channel error:', error);
        setConnectionStatus('error');
      };

      pc.ontrack = (event) => {
        const [remoteStream] = event.streams;
        const audio = new Audio();
        audio.srcObject = remoteStream;
        audio.play().catch(console.error);
      };

      const offer = await pc.createOffer();
      await pc.setLocalDescription(offer);

      const response = await fetch(`https://api.openai.com/v1/realtime?model=gpt-4o-realtime-preview-2024-12-17`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${sessionData.client_secret.value}`,
          'Content-Type': 'application/sdp',
        },
        body: offer.sdp,
      });

      if (!response.ok) {
        throw new Error(`WebRTC setup failed: ${response.status}`);
      }

      const answerSdp = await response.text();
      await pc.setRemoteDescription({
        type: 'answer',
        sdp: answerSdp,
      });
    } catch (error) {
      console.error('WebRTC setup error:', error);
      setConnectionStatus('error');
      throw error;
    }
  };

  const handleRealtimeEvent = (event) => {
    console.log('Realtime event:', event);

    switch (event.type) {
      case 'conversation.item.created':
        if (event.item.type === 'message') {
          const message = {
            id: event.item.id,
            text: event.item.content?.[0]?.text || '',
            sender: event.item.role === 'user' ? 'user' : 'ai',
            agent: selectedAgent,
            timestamp: new Date().toLocaleTimeString()
          };
          setMessages(prev => [...prev, message]);
        }
        break;

      case 'response.audio_transcript.delta':
        setMessages(prev => {
          const updated = [...prev];
          const lastMessage = updated[updated.length - 1];
          if (lastMessage && lastMessage.sender === 'ai') {
            lastMessage.text += event.delta;
          }
          return updated;
        });
        break;

      case 'response.audio.delta':
        setIsSpeaking(true);
        break;

      case 'response.done':
        setIsSpeaking(false);
        setIsProcessing(false); // Clear processing indicator when response is complete
        break;

      case 'response.function_call.done':
        setMessages(prev => {
          const lastMsg = prev[prev.length - 1];
          if (lastMsg && lastMsg.isTool) {
            try {
              const finalArgs = JSON.parse(lastMsg.toolArgs);
              return [...prev.slice(0, -1), { ...lastMsg, text: `Used ${lastMsg.toolName}(${JSON.stringify(finalArgs, null, 2)})`, toolArgs: finalArgs, isToolCompleted: true }];
            } catch (e) {
              return [...prev.slice(0, -1), { ...lastMsg, text: `Used ${lastMsg.toolName} with arguments.`, isToolCompleted: true }];
            }
          }
          return prev;
        });
        break;

      case 'response.output_item.tool_output':
        setMessages(prev => [...prev, {
          id: event.item_id,
          sender: 'system',
          text: `Tool Output: ${JSON.stringify(event.output, null, 2)}`,
          timestamp: new Date().toLocaleTimeString(),
        }]);
        break;

      case 'response.output_item.text.delta':
        setMessages(prev => {
          const lastMsg = prev[prev.length - 1];
          if (lastMsg && lastMsg.sender === 'agent' && !lastMsg.isTool) {
            return [...prev.slice(0, -1), { ...lastMsg, text: lastMsg.text + event.delta }];
          } else {
            return [...prev, { id: event.item_id, sender: 'agent', text: event.delta, timestamp: new Date().toLocaleTimeString() }];
          }
        });
        break;

      case 'response.output_item.text.done':
        break;

      case 'input_audio_buffer.speech_started':
        setIsListening(true);
        break;

      case 'input_audio_buffer.speech_stopped':
        setIsListening(false);
        setIsProcessing(true); // Start processing when user stops speaking
        break;

      case 'tool_calls':
        const toolPromises = event.tool_calls.map(async (toolCall) => {
          const { toolName, arguments: toolArgs } = toolCall.function;

          const response = await fetch(`${API_BASE}/realtime/tools`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ toolName, arguments: toolArgs })
          });

          const result = await response.json();

          dataChannelRef.current.send(JSON.stringify({
            type: 'tool_outputs',
            tool_outputs: [{
              tool_call_id: toolCall.id,
              output: JSON.stringify(result)
            }]
          }));
        });

        Promise.all(toolPromises).catch(error => {
          console.error('Error handling tool calls:', error);
        });
        break;

      case 'error':
        console.error('Realtime API error:', event.error);
        setConnectionStatus('error');
        break;
    }
  };

  const connect = async () => {
    try {
      const sessionData = await createRealtimeSession();
      await setupWebRTC(sessionData);

      const welcomeMessage = {
        id: Date.now(),
        text: `Hello! I'm JARVIS in realtime mode. I can now have natural voice conversations with you. How can I help you today?`,
        sender: 'ai',
        agent: selectedAgent,
        timestamp: new Date().toLocaleTimeString()
      };
      setMessages([welcomeMessage]);
    } catch (error) {
      console.error('Connection failed:', error);
      setConnectionStatus('error');
    } finally {
      setIsConnecting(false);
    }
  };

  const disconnect = () => {
    if (pcRef.current) {
      pcRef.current.close();
      pcRef.current = null;
    }

    if (dataChannelRef.current) {
      dataChannelRef.current.close();
      dataChannelRef.current = null;
    }

    if (microphoneStreamRef.current) {
      microphoneStreamRef.current.getTracks().forEach(track => track.stop());
      microphoneStreamRef.current = null;
    }

    setIsConnected(false);
    setConnectionStatus('disconnected');
    setMessages([]);
  };

  const sendTextMessage = (text) => {
    if (!dataChannelRef.current || !text.trim()) return;

    const userMessage = {
      id: Date.now(),
      text,
      sender: 'user',
      timestamp: new Date().toLocaleTimeString()
    };

    setMessages(prev => [...prev, userMessage]);
    setIsProcessing(true); // Start processing indicator

    dataChannelRef.current.send(JSON.stringify({
      type: 'conversation.item.create',
      item: {
        type: 'message',
        role: 'user',
        content: [{
          type: 'input_text',
          text: text
        }]
      }
    }));

    dataChannelRef.current.send(JSON.stringify({
      type: 'response.create'
    }));
  };

  const changeAgent = async (agentName) => {
    if (agentName === selectedAgent) return;

    setSelectedAgent(agentName);

    if (isConnected) {
      disconnect();
      setTimeout(connect, 100);
    }
  };

  return (
    <div className="flex h-screen bg-gray-900 text-white font-sans">
      {backgroundElements.map(el => (
        <div
          key={el.id}
          className="absolute bg-gradient-to-br from-cyan-500/10 to-purple-500/10 rounded-full blur-3xl"
          style={{
            width: `${el.width}px`,
            height: `${el.height}px`,
            left: `${el.left}%`,
            top: `${el.top}%`,
            animation: `float ${el.animationDuration}s ease-in-out ${el.animationDelay}s infinite alternate`
          }}
        />
      ))}

      <div className="w-80 bg-gray-900/50 border-r border-gray-800 p-6 flex flex-col">
        <h1 className="text-2xl font-bold mb-6 text-cyan-400">JARVIS Realtime</h1>

        <div className="mb-4">
          <label htmlFor="agent-select" className="block text-sm font-medium text-gray-400 mb-2">Select Agent</label>
          <select
            id="agent-select"
            value={selectedAgent}
            onChange={(e) => changeAgent(e.target.value)}
            disabled={isConnected}
            className="w-full bg-gray-700 border border-gray-600 rounded-md p-2 text-white focus:ring-2 focus:ring-cyan-500"
          >
            {aiAgents.map(agent => (
              <option key={agent.id} value={agent.id}>{agent.name}</option>
            ))}
          </select>
        </div>

        <div className="mb-4">
          <label htmlFor="voice-select" className="block text-sm font-medium text-gray-400 mb-2">Select Voice</label>
          <select 
            id="voice-select"
            value={selectedVoice}
            onChange={(e) => setSelectedVoice(e.target.value)}
            disabled={isConnected}
            className="w-full bg-gray-700 border border-gray-600 rounded-md p-2 text-white focus:ring-2 focus:ring-cyan-500"
          >
            <option value="alloy">Alloy</option>
            <option value="echo">Echo</option>
            <option value="fable">Fable</option>
            <option value="onyx">Onyx</option>
            <option value="nova">Nova</option>
            <option value="shimmer">Shimmer</option>
          </select>
        </div>

        <div className="mt-auto text-center text-xs text-gray-500">
          <p>Status: <span className={`font-bold ${connectionStatus === 'connected' ? 'text-green-400' : 'text-yellow-400'}`}>{connectionStatus}</span></p>
          <p>Powered by OpenAI & Windsurf</p>
        </div>
      </div>

      <div className="flex-1 flex flex-col">
        <div className="flex-1 flex items-center justify-center">
          <div className="relative">
            <div className={`w-64 h-64 rounded-full relative transition-all duration-300 ${isListening || isSpeaking || isProcessing ? 'scale-110' : 'scale-100'}`}>
              <div className={`absolute inset-0 rounded-full border-2 border-cyan-400/30 ${isListening || isSpeaking || isProcessing ? 'animate-ping' : ''}`} />
              <div className={`absolute inset-0 rounded-full border-2 border-blue-400/20 ${isListening || isSpeaking || isProcessing ? 'animate-ping animation-delay-200' : ''}`} />
              {isProcessing && (
                <div className="absolute inset-0 rounded-full border-2 border-yellow-400/40 animate-pulse" />
              )}

              <div className={`absolute inset-2 rounded-full bg-gradient-to-br ${aiAgents.find(a => a.id === selectedAgent)?.color || 'from-gray-700 to-gray-800'} flex items-center justify-center shadow-lg`}>
                <button
                  onClick={isConnected ? disconnect : connect}
                  disabled={isConnecting}
                  className="w-48 h-48 rounded-full bg-black/20 flex items-center justify-center text-white/80 hover:text-white transition-all duration-300 focus:outline-none focus:ring-4 focus:ring-cyan-400/50"
                >
                  {isConnecting ? (
                    <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-white"></div>
                  ) : isConnected ? (
                    <PhoneOff size={60} className={isListening ? "animate-pulse" : ""} />
                  ) : (
                    <Phone size={60} />
                  )}
                </button>
              </div>

              <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 text-center">
                <p className="text-sm text-gray-300 font-medium">
                  {isConnected ? (isListening ? 'Listening...' : isSpeaking ? 'Speaking...' : isProcessing ? 'Processing...' : 'Connected') : 'Click to Connect'}
                </p>
                <p className="text-xs text-gray-500">
                  {aiAgents.find(a => a.id === selectedAgent)?.name}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="flex-1 overflow-y-auto p-6 space-y-4 max-h-64">
          {messages.map(msg => (
            <div key={msg.id} className={`flex ${msg.sender === 'user' ? 'justify-end' : 'justify-start'}`}>
              <div className={`max-w-lg p-3 rounded-lg ${msg.sender === 'user' ? 'bg-blue-600' : 'bg-gray-700'}`}>
                <p className="text-sm">{msg.text}</p>
                <p className="text-xs text-gray-400 mt-1 text-right">{msg.timestamp}</p>
              </div>
            </div>
          ))}
          {isProcessing && (
            <div className="flex justify-start">
              <div className="max-w-lg p-3 rounded-lg bg-gray-700 border border-gray-600">
                <div className="flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-cyan-400"></div>
                  <p className="text-sm text-gray-300">JARVIS is thinking...</p>
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="p-6">
          <form onSubmit={(e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            const text = formData.get('message');
            if (text) {
              sendTextMessage(text);
              e.target.reset();
            }
          }} className="flex items-center bg-gray-900/50 border border-gray-700 rounded-full p-2">
            <input
              name="message"
              type="text"
              placeholder={isConnected ? 'Type a message or speak naturally...' : 'Connect to start chatting'}
              className="flex-1 bg-transparent text-white placeholder-gray-500 focus:outline-none px-4"
              disabled={!isConnected}
            />
            <button
              type="submit"
              className="bg-blue-500 hover:bg-blue-600 rounded-full p-3 text-white transition-colors duration-300 disabled:opacity-50"
              disabled={!isConnected}
            >
              <Send size={20} />
            </button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default JarvisRealtimeAgent;
