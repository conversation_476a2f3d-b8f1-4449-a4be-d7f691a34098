import { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useLocation } from 'react-router-dom';
import { AuthProvider, useAuth } from './contexts/AuthContext.jsx';
import JarvisAgent from './components/JarvisAgent.jsx';
import JarvisRealtimeAgent from './components/JarvisRealtimeAgent.jsx';
import N8nWorkflowTrigger from './components/N8nWorkflowTrigger.jsx';
import Login from './components/Login.jsx';
import UserProfile from './components/UserProfile.jsx';
import { ToggleLeft, ToggleRight, Zap, MessageSquare, Mic } from 'lucide-react';

function AuthCallbackHandler() {
  const { handleAuthCallback } = useAuth();
  const [processed, setProcessed] = useState(false);

  useEffect(() => {
    const handled = handleAuthCallback();
    if (handled) {
      setProcessed(true);
      // Redirect to home after processing
      window.location.replace('/');
    } else {
      // No auth data, redirect to login
      window.location.replace('/');
    }
  }, [handleAuthCallback]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div className="text-white">Processing authentication...</div>
    </div>
  );
}

function MainApp() {
  const [currentMode, setCurrentMode] = useState('assistant'); // 'assistant', 'realtime', 'workflows'
  const { user, loading } = useAuth();
  const location = useLocation();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        <div className="text-white">Loading...</div>
      </div>
    );
  }

  if (!user) {
    return <Login />;
  }

  return (
    <div className="relative">
      {/* User Profile and Mode Toggle */}
      <div className="absolute top-4 right-4 z-50 flex items-center space-x-4">
        <div className="bg-gray-800/90 backdrop-blur-sm rounded-lg p-3 border border-gray-700">
          <div className="flex items-center gap-2">
            {/* Assistant API Mode */}
            <button
              onClick={() => setCurrentMode('assistant')}
              className={`flex items-center space-x-1 px-3 py-2 rounded-lg transition-colors ${
                currentMode === 'assistant' 
                  ? 'bg-blue-600 text-white' 
                  : 'text-gray-400 hover:text-white hover:bg-gray-700'
              }`}
            >
              <MessageSquare size={16} />
              <span className="text-sm">Chat</span>
            </button>
            
            {/* Realtime API Mode */}
            <button
              onClick={() => setCurrentMode('realtime')}
              className={`flex items-center space-x-1 px-3 py-2 rounded-lg transition-colors ${
                currentMode === 'realtime' 
                  ? 'bg-green-600 text-white' 
                  : 'text-gray-400 hover:text-white hover:bg-gray-700'
              }`}
            >
              <Mic size={16} />
              <span className="text-sm">Voice</span>
            </button>
            
            {/* N8N Workflows Mode */}
            <button
              onClick={() => setCurrentMode('workflows')}
              className={`flex items-center space-x-1 px-3 py-2 rounded-lg transition-colors ${
                currentMode === 'workflows' 
                  ? 'bg-yellow-600 text-white' 
                  : 'text-gray-400 hover:text-white hover:bg-gray-700'
              }`}
            >
              <Zap size={16} />
              <span className="text-sm">Workflows</span>
            </button>
          </div>
          
          <div className="text-xs text-gray-500 mt-2 text-center">
            {currentMode === 'assistant' && 'Traditional chat interface'}
            {currentMode === 'realtime' && 'Real-time voice conversation'}
            {currentMode === 'workflows' && 'N8N workflow orchestrator'}
          </div>
        </div>
        <UserProfile />
      </div>

      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        {currentMode === 'assistant' && <JarvisAgent />}
        {currentMode === 'realtime' && <JarvisRealtimeAgent />}
        {currentMode === 'workflows' && (
          <div className="flex items-center justify-center min-h-screen p-6">
            <N8nWorkflowTrigger />
          </div>
        )}
      </div>
    </div>
  );
}

function App() {
  return (
    <AuthProvider>
      <Router future={{ v7_relativeSplatPath: true }}>
        <Routes>
          <Route path="/auth/callback" element={<AuthCallbackHandler />} />
          <Route path="/" element={<MainApp />} />
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </Router>
    </AuthProvider>
  );
}

export default App;
