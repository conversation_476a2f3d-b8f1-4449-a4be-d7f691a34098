# Multi-stage build for full-stack JARVIS application
# Stage 1: Frontend Builder
FROM node:20-slim AS frontend-builder

# Install pnpm globally
RUN npm install -g pnpm

# Set working directory
WORKDIR /app

# Copy workspace configuration
COPY pnpm-workspace.yaml ./
COPY package.json ./
COPY pnpm-lock.yaml ./

# Install frontend dependencies
RUN pnpm install --frozen-lockfile

# Copy frontend source code
COPY src/ ./src/
COPY index.html ./
COPY vite.config.js ./
COPY tailwind.config.js ./
COPY postcss.config.js ./
COPY .eslintrc.json ./

# Build frontend for production
RUN pnpm run build

# Stage 2: Backend Builder
FROM node:20-slim AS backend-builder

# Install curl for health checks
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy backend package files
COPY backend/package*.json ./

# Install backend dependencies (production only)
RUN npm ci --omit=dev

# Stage 3: Production Runtime
FROM node:20-slim AS production

# Install curl for health checks
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

# Create app user for security
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Set working directory
WORKDIR /app

# Copy backend dependencies
COPY --from=backend-builder /app/node_modules ./node_modules

# Copy backend source code
COPY backend/ ./

# Copy built frontend from frontend-builder stage
COPY --from=frontend-builder /app/dist ./public

# Create uploads directory and set permissions
RUN mkdir -p uploads && chown -R appuser:appuser /app

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 5050

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
  CMD curl -f http://localhost:5050/health || exit 1

# Start the application
CMD ["node", "server.js"]

# Stage 4: Development Environment
FROM node:20-slim AS development

# Install pnpm globally and curl
RUN npm install -g pnpm && \
    apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy workspace configuration
COPY pnpm-workspace.yaml ./
COPY package.json ./
COPY pnpm-lock.yaml ./

# Install all dependencies (including dev)
RUN pnpm install --frozen-lockfile

# Copy backend package files and install backend deps
COPY backend/package*.json ./backend/
WORKDIR /app/backend
RUN npm install

# Go back to root
WORKDIR /app

# Create uploads directory
RUN mkdir -p uploads backend/uploads

# Expose ports for both frontend and backend
EXPOSE 5175 5050

# Default command for development
CMD ["pnpm", "run", "dev"]
