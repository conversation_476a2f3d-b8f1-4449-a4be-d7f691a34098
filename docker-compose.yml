

services:
  # Production Application
  jarvis-app:
    build:
      context: .
      target: production
    ports:
      - "5050:5050"
    environment:
      - NODE_ENV=production
      - PORT=5050
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_ASSISTANT_ID=${OPENAI_ASSISTANT_ID}
      - N8N_BASE_URL=${N8N_BASE_URL}
      - N8N_API_KEY=${N8N_API_KEY}
      - N8N_WEBHOOK_URL=${N8N_WEBHOOK_URL}
      - N8N_FINANCIAL_WEBHOOK_URL=${N8N_FINANCIAL_WEBHOOK_URL}
      - N8N_EMAIL_WEBHOOK_URL=${N8N_EMAIL_WEBHOOK_URL}
      - N8N_RECEIPTS_WEBHOOK_URL=${N8N_RECEIPTS_WEBHOOK_URL}
      - N8N_GENERAL_WEBHOOK_URL=${N8N_GENERAL_WEBHOOK_URL}
      - CORS_ORIGIN=${CORS_ORIGIN:-http://localhost:5050}
      - WEATHER_API_KEY=${WEATHER_API_KEY}
    volumes:
      - ./uploads:/app/uploads
      - ./backend/database.sqlite:/app/database.sqlite
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5050/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    profiles:
      - production

  # Development Environment
  jarvis-dev:
    build:
      context: .
      target: development
    ports:
      - "5173:5173"  # Vite dev server
      - "5050:5050"  # Backend API
    environment:
      - NODE_ENV=development
      - PORT=5050
      - VITE_PORT=5173
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_ASSISTANT_ID=${OPENAI_ASSISTANT_ID}
      - N8N_BASE_URL=${N8N_BASE_URL}
      - N8N_API_KEY=${N8N_API_KEY}
      - N8N_WEBHOOK_URL=${N8N_WEBHOOK_URL}
      - N8N_FINANCIAL_WEBHOOK_URL=${N8N_FINANCIAL_WEBHOOK_URL}
      - N8N_EMAIL_WEBHOOK_URL=${N8N_EMAIL_WEBHOOK_URL}
      - N8N_RECEIPTS_WEBHOOK_URL=${N8N_RECEIPTS_WEBHOOK_URL}
      - N8N_GENERAL_WEBHOOK_URL=${N8N_GENERAL_WEBHOOK_URL}
      - CORS_ORIGIN=http://localhost:5173
      - WEATHER_API_KEY=${WEATHER_API_KEY}
    volumes:
      - .:/app
      - /app/node_modules
      - /app/backend/node_modules
      - ./uploads:/app/uploads
      - ./backend/database.sqlite:/app/backend/database.sqlite
    command: |
      sh -c '
        # Start backend in background
        cd backend && npm run dev &
        # Start frontend dev server
        cd /app && pnpm run dev --host 0.0.0.0
      '
    profiles:
      - development

  # Reverse Proxy for Production (SSL termination)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - jarvis-app
    restart: unless-stopped
    profiles:
      - production

  # Database for development (optional SQLite alternative)
  postgres-dev:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=jarvis_dev
      - POSTGRES_USER=jarvis
      - POSTGRES_PASSWORD=jarvis_dev_password
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    ports:
      - "5432:5432"
    profiles:
      - development
      - postgres

volumes:
  postgres_dev_data:

networks:
  default:
    name: jarvis-network
