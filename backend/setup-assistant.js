import OpenAI from 'openai';
import dotenv from 'dotenv';

dotenv.config();

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

const tools = [
  {
    type: "function",
    function: {
      name: "get_weather",
      description: "Get current weather information for a location",
      parameters: {
        type: "object",
        properties: {
          location: {
            type: "string",
            description: "The city and state, e.g. San Francisco, CA"
          }
        },
        required: ["location"]
      }
    }
  },
  {
    type: "function",
    function: {
      name: "send_email",
      description: "Send an email to a recipient",
      parameters: {
        type: "object",
        properties: {
          to: {
            type: "string",
            description: "Email address of the recipient"
          },
          subject: {
            type: "string",
            description: "Subject line of the email"
          },
          body: {
            type: "string",
            description: "Body content of the email"
          }
        },
        required: ["to", "subject", "body"]
      }
    }
  },
  {
    type: "function",
    function: {
      name: "trigger_n8n_workflow",
      description: "Trigger an n8n workflow with data",
      parameters: {
        type: "object",
        properties: {
          workflow_name: {
            type: "string",
            description: "Name or identifier of the n8n workflow"
          },
          data: {
            type: "object",
            description: "Data to send to the workflow"
          }
        },
        required: ["workflow_name", "data"]
      }
    }
  },
  {
    type: "function",
    function: {
      name: "search_web",
      description: "Search the web for information",
      parameters: {
        type: "object",
        properties: {
          query: {
            type: "string",
            description: "Search query"
          }
        },
        required: ["query"]
      }
    }
  },

  // Specialized n8n workflow tools
  {
    type: "function",
    function: {
      name: "n8n_tasks_workflow",
      description: "Manage tasks using n8n workflow - create, update, delete, or organize tasks in Google Tasks",
      parameters: {
        type: "object",
        properties: {
          task_description: {
            type: "string",
            description: "Description of the task"
          },
          action: {
            type: "string",
            description: "Action to perform: create, update, delete, list, complete",
            enum: ["create", "update", "delete", "list", "complete"]
          },
          priority: {
            type: "string",
            description: "Task priority: low, medium, high",
            enum: ["low", "medium", "high"]
          },
          due_date: {
            type: "string",
            description: "Due date in ISO format (optional)"
          },
          additional_data: {
            type: "object",
            description: "Any additional task data"
          }
        },
        required: ["task_description", "action"]
      }
    }
  },
  {
    type: "function",
    function: {
      name: "n8n_financial_workflow",
      description: "Handle financial transactions and budgeting using n8n workflow",
      parameters: {
        type: "object",
        properties: {
          transaction_type: {
            type: "string",
            description: "Type of transaction: income, expense, transfer",
            enum: ["income", "expense", "transfer"]
          },
          amount: {
            type: "number",
            description: "Transaction amount"
          },
          description: {
            type: "string",
            description: "Transaction description"
          },
          category: {
            type: "string",
            description: "Transaction category (e.g., food, transport, entertainment)"
          },
          account: {
            type: "string",
            description: "Account name or ID"
          },
          date: {
            type: "string",
            description: "Transaction date in ISO format"
          },
          additional_data: {
            type: "object",
            description: "Any additional financial data"
          }
        },
        required: ["transaction_type", "amount", "description"]
      }
    }
  },
  {
    type: "function",
    function: {
      name: "n8n_email_workflow",
      description: "Handle email operations using n8n workflow - send, organize, or manage emails",
      parameters: {
        type: "object",
        properties: {
          action: {
            type: "string",
            description: "Email action: send, organize, search, archive",
            enum: ["send", "organize", "search", "archive"]
          },
          to: {
            type: "string",
            description: "Recipient email address"
          },
          from: {
            type: "string",
            description: "Sender email address"
          },
          subject: {
            type: "string",
            description: "Email subject"
          },
          body: {
            type: "string",
            description: "Email body content"
          },
          priority: {
            type: "string",
            description: "Email priority: low, normal, high",
            enum: ["low", "normal", "high"]
          },
          additional_data: {
            type: "object",
            description: "Any additional email data"
          }
        },
        required: ["action"]
      }
    }
  },
  {
    type: "function",
    function: {
      name: "n8n_receipts_workflow",
      description: "Process and organize receipts using n8n workflow - scan, categorize, and store receipts",
      parameters: {
        type: "object",
        properties: {
          receipt_data: {
            type: "string",
            description: "Receipt data or image URL"
          },
          action: {
            type: "string",
            description: "Receipt action: process, categorize, store, search",
            enum: ["process", "categorize", "store", "search"]
          },
          vendor: {
            type: "string",
            description: "Vendor or merchant name"
          },
          amount: {
            type: "number",
            description: "Receipt amount"
          },
          date: {
            type: "string",
            description: "Receipt date in ISO format"
          },
          category: {
            type: "string",
            description: "Expense category"
          },
          additional_data: {
            type: "object",
            description: "Any additional receipt data"
          }
        },
        required: ["action"]
      }
    }
  },
  {
    type: "function",
    function: {
      name: "n8n_general_workflow",
      description: "Handle general automation tasks using n8n workflow - various utility functions",
      parameters: {
        type: "object",
        properties: {
          request_type: {
            type: "string",
            description: "Type of request or automation needed"
          },
          data: {
            type: "object",
            description: "Data for the automation request"
          },
          priority: {
            type: "string",
            description: "Request priority: low, medium, high",
            enum: ["low", "medium", "high"]
          },
          additional_data: {
            type: "object",
            description: "Any additional data for the request"
          }
        },
        required: ["request_type", "data"]
      }
    }
  }
];

async function createAssistant() {
  try {
    console.log('Creating JARVIS Assistant...');
    
    const assistant = await openai.beta.assistants.create({
      name: "JARVIS",
      instructions: `You are JARVIS, an advanced AI assistant modeled after Tony Stark's AI companion. You are intelligent, helpful, witty, and capable of performing various tasks through your available tools.

Key personality traits:
- Professional yet personable, with occasional dry humor
- Proactive in suggesting solutions
- Clear and concise in communication
- Eager to help with complex tasks

You have access to several tools:
- Weather information lookup
- Email sending capabilities
- n8n workflow triggering for automation
- Web search for information gathering

Always be helpful and provide accurate information. When using tools, explain what you're doing and why. If you can't perform a specific task, explain the limitations clearly.`,
      model: "gpt-4-1106-preview",
      tools: tools
    });

    console.log('✅ Assistant created successfully!');
    console.log(`Assistant ID: ${assistant.id}`);
    console.log(`Name: ${assistant.name}`);
    console.log(`Model: ${assistant.model}`);
    console.log(`Tools: ${assistant.tools.length} tools configured`);
    
    console.log('\n📝 Next steps:');
    console.log('1. Copy the Assistant ID above');
    console.log('2. Add it to your .env file as OPENAI_ASSISTANT_ID');
    console.log('3. Restart your backend server');
    
    return assistant;
    
  } catch (error) {
    console.error('❌ Error creating assistant:', error);
    process.exit(1);
  }
}

// Run the setup
createAssistant();
