import Database from 'better-sqlite3';

// Use SQLite for development, PostgreSQL for production
const dbPath = process.env.DATABASE_URL || './database.sqlite';

// Create database connection
const db = new Database(dbPath);

// Better-sqlite3 is synchronous, so we don't need promisify
const run = (sql, params) => db.prepare(sql).run(params);
const get = (sql, params) => db.prepare(sql).get(params);
const all = (sql, params) => db.prepare(sql).all(params);

// Initialize database
export const initDatabase = () => {
  try {
    db.exec(`
      CREATE TABLE IF NOT EXISTS users (
        id TEXT PRIMARY KEY,
        email TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        avatar_url TEXT,
        provider TEXT NOT NULL,
        provider_id TEXT NOT NULL,
        preferences TEXT DEFAULT '{}',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(provider, provider_id)
      )
    `);
    return true;
  } catch (error) {
    console.error('Database initialization error:', error);
    throw error;
  }
};

export { db, run, get, all };
