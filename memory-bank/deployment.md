# JARVIS AI Assistant - Deployment Guide

## Overview
This document provides comprehensive deployment instructions for the JARVIS AI Assistant, including Railway (backend) and Netlify (frontend) deployment configurations.

## Architecture
- **Frontend**: React + Vite (deployed to Netlify)
- **Backend**: Node.js + Express + OpenAI (deployed to Railway)
- **AI Integration**: OpenAI Assistants API + Realtime API
- **Workflow Engine**: n8n integration for task automation

## Environment Variables

### Backend (.env)
```bash
# OpenAI Configuration
OPENAI_API_KEY=********************************************************************************************************************************************************************
OPENAI_ASSISTANT_ID=asst_yOkCntxRexltTdMQVZ0vSuMw

# Server Configuration
PORT=5050
REALTIME_PORT=5051
NODE_ENV=production

# Security
CORS_ORIGIN=https://your-frontend-domain.netlify.app

# n8n Integration
N8N_BASE_URL=https://n8n.scrapha.com
N8N_API_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMDMxZjljMy03MmM2LTQxMjktOGYxMC0xN2EzYzE4Y2MyM2UiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzUxODA0MTgzfQ.w5Dsq7tC8VrIr7hcQZpD96lus624bN9gyJkCTFmS2xg
N8N_WEBHOOK_URL=https://n8n.scrapha.com/webhook/google-tasks-voice-agent
N8N_FINANCIAL_WEBHOOK_URL=https://n8n.scrapha.com/webhook/financial-agent
N8N_EMAIL_WEBHOOK_URL=https://n8n.scrapha.com/webhook/email-assistant
N8N_RECEIPTS_WEBHOOK_URL=https://n8n.scrapha.com/webhook/receipt-sorter
N8N_GENERAL_WEBHOOK_URL=https://n8n.scrapha.com/webhook/general-assistant

# Optional: External API Keys
WEATHER_API_KEY=your_weather_api_key_here
```

### Frontend (.env.local)
```bash
VITE_API_BASE_URL=https://your-backend-domain.up.railway.app/api
```

## Railway Deployment (Backend)

### 1. Railway Configuration
- **Service**: Node.js backend
- **Build Command**: `npm install`
- **Start Command**: `node server.js`
- **Health Check**: `/health`

### 2. Railway.json Configuration
```json
{
  "$schema": "https://railway.app/railway.schema.json",
  "build": {
    "builder": "NIXPACKS"
  },
  "deploy": {
    "startCommand": "node server.js",
    "healthcheckPath": "/health",
    "healthcheckTimeout": 100,
    "restartPolicyType": "ON_FAILURE",
    "restartPolicyMaxRetries": 10
  }
}
```

### 3. Deployment Steps
1. **Connect Repository**: Link your GitHub repository to Railway
2. **Environment Variables**: Add all backend environment variables in Railway dashboard
3. **Deploy**: Railway will auto-deploy on push to main branch
4. **Custom Domain**: Configure custom domain if needed

## Netlify Deployment (Frontend)

### 1. Netlify Configuration
- **Build Command**: `npm run build`
- **Publish Directory**: `dist`
- **Node Version**: 18.x

### 2. Netlify.toml Configuration
```toml
[build]
  publish = "dist"
  command = "npm run build"

[build.environment]
  NODE_VERSION = "18"

[[redirects]]
  from = "/api/*"
  to = "https://your-backend-domain.up.railway.app/api/:splat"
  status = 200

# Redirects for SPA routing
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
```

### 3. Deployment Steps
1. **Connect Repository**: Link your GitHub repository to Netlify
2. **Environment Variables**: Add `VITE_API_BASE_URL` in Netlify dashboard
3. **Build Settings**: Configure build command and publish directory
4. **Deploy**: Netlify will auto-deploy on push to main branch

## API Endpoints

### Backend Endpoints
- `GET /health` - Health check
- `POST /api/thread` - Create new conversation thread
- `POST /api/chat` - Send message to AI assistant
- `GET /api/assistant` - Get assistant information
- `POST /api/realtime/session` - Create realtime session

### Frontend Configuration
- **Base URL**: Configured via `VITE_API_BASE_URL`
- **CORS**: Configured for production domains
- **File Upload**: Supports file attachments

## n8n Workflow Integration

### Available Workflows
1. **financial-agent**: Budgeting and financial planning
2. **google-tasks-voice-agent**: Task organization and management
3. **email-assistant**: Email composition and management
4. **receipt-sorter**: Document categorization and sorting
5. **general-assistant**: General purpose AI assistance

### Webhook URLs
- Base: `https://n8n.scrapha.com/webhook/`
- Authentication: Bearer token via `N8N_API_KEY`

## Security Considerations

### Backend Security
- Rate limiting (100 requests per 15 minutes)
- CORS configured for production domains
- Helmet.js for security headers
- Input validation and sanitization

### Frontend Security
- Environment variables for API URLs
- HTTPS enforcement
- Input validation

## Monitoring & Logs

### Railway Logs
- Access via Railway dashboard
- Real-time log streaming
- Error tracking and alerts

### Netlify Analytics
- Build logs and deployment status
- Performance monitoring
- Error tracking

## Troubleshooting

### Common Issues

1. **CORS Errors**
   - Ensure `CORS_ORIGIN` matches frontend domain
   - Check Netlify redirects configuration

2. **API Key Issues**
   - Verify OpenAI API key is valid
   - Check n8n API key permissions

3. **Build Failures**
   - Ensure Node.js version compatibility
   - Check environment variables are set

4. **Deployment Issues**
   - Verify all environment variables are configured
   - Check build logs for specific errors

### Health Check Commands
```bash
# Backend health
curl https://your-backend-domain.up.railway.app/health

# Frontend deployment
curl https://your-frontend-domain.netlify.app
```

## Performance Optimization

### Backend
- Enable gzip compression
- Implement caching strategies
- Optimize database queries

### Frontend
- Enable asset optimization in Netlify
- Implement lazy loading
- Optimize images and assets

## Backup & Recovery

### Database
- Implement regular backups
- Document recovery procedures

### Configuration
- Store environment variables securely
- Maintain deployment scripts

## Support & Maintenance

### Updates
- Regular dependency updates
- Security patches
- Feature enhancements

### Monitoring
- Set up alerts for downtime
- Monitor API usage and costs
- Track performance metrics
