# Active Context

- The application is a voice-activated AI assistant named <PERSON><PERSON><PERSON><PERSON>.
- The frontend is a React + Vite application using Tailwind CSS.
- The core UI and features are implemented and functional.
- **CURRENT STATUS**: Application is fully running with complete n8n workflow integration.

## Current Task Status: ✅ SQLITE BINDING ISSUE RESOLVED & DOCKER CONFIG UPDATED (2025-08-03)
- **RESOLVED**: SQLite3 binding issues by migrating to better-sqlite3
- **UPDATED**: Docker configuration with correct ports (5175 for frontend, 5050 for backend)
- **VERIFIED**: Database initialization working with better-sqlite3
- **TESTED**: Mock authentication with database integration working
- **CONFIGURED**: All environment variables updated for correct port usage

## Previous Completed: ✅ N8N WORKFLOW INTEGRATION COMPLETE (2025-07-30)
- **COMPLETED**: 5 specialized n8n workflow tools integrated into both assistants
- **UPDATED**: Traditional assistant with all n8n workflow functions in server.js
- **UPDATED**: Realtime assistant agents with specialized n8n tools in realtime-server.js
- **CREATED**: New OpenAI assistant (asst_yOkCntxRexltTdMQVZ0vSuMw) with 9 tools total
- **ADDED**: New receipts agent for receipt processing and expense tracking
- **CONFIGURED**: All n8n webhook URLs and API authentication
- **UPDATED**: Docker configuration with n8n environment variables
- **VERIFIED**: Setup script successfully creates assistant with all tools

## Previous Completed Tasks
- **COMPLETED**: Docker multi-stage build configuration with development and production targets
- **CREATED**: Comprehensive docker-compose.yml with profiles for dev/prod environments
- **IMPLEMENTED**: Docker setup script (docker-setup.sh) for easy container management
- **DOCUMENTED**: Complete Docker guide (DOCKER_GUIDE.md) with deployment instructions
- **FIXED**: Local development authentication issues resolved
- **FIXED**: Realtime voice assistant 500 error - tools format compatibility issue resolved
- **VERIFIED**: Full application stack working locally including realtime voice functionality

## Current Status (2025-07-15)
- **Tech Stack**: React + Vite, Node.js + Express, Tailwind CSS, `pnpm`
- **Frontend**: Vite-based React app with proper build configuration
- **Backend**: Node.js + Express server for OpenAI integration
- **Package Management**: Using `pnpm` with proper workspace configuration
- **Build System**: Vite build tool with correct scripts

## Current Status
- Frontend is built and ready for deployment
- Backend integration is complete with OpenAI API
- Environment variables are configured
- **FIXED**: Netlify build failing with "next: not found" error
- **RESOLVED**: Package.json now has correct Vite build scripts
- **VERIFIED**: Build command is `pnpm run build` (Vite build)
- **VERIFIED**: Publish directory is `dist` (Vite output)
- **READY**: Netlify deployment should now work correctly

## Key Components Identified
- **JarvisAgent**: The main React component containing all the application logic.
- **JarvisRealtimeAgent**: Component for voice-to-voice AI interactions
- **Features**: Speech-to-text, text-to-speech, file upload, AI agent routing, and n8n workflow automation
- **Dependencies**: React, Vite, Tailwind CSS, Express backend
- **N8N Integration**: 5 specialized workflow tools for tasks, finance, email, receipts, and general automation

## N8N Workflow Tools Available
1. **n8n_tasks_workflow**: Google Tasks integration (create, update, delete, list, complete)
2. **n8n_financial_workflow**: Financial transaction processing and budgeting
3. **n8n_email_workflow**: Advanced email operations (send, organize, search, archive)
4. **n8n_receipts_workflow**: Receipt processing, categorization, and expense tracking
5. **n8n_general_workflow**: General automation tasks and utility functions

## Realtime Assistant Agents
- **General Agent**: Weather, web search, general n8n workflow, handoffs to specialized agents
- **Financial Agent**: Financial workflow integration, expense tracking
- **Task Agent**: Google Tasks workflow integration, task management
- **Email Agent**: Email workflow integration, email operations
- **Receipts Agent**: Receipt processing workflow, expense categorization

## Build Configuration
- **Build Command**: `pnpm run build` (Vite build)
- **Publish Directory**: `dist` (Vite output directory)
- **Package Manager**: pnpm
- **Node Version**: 18.x
- **OpenAI Assistant**: asst_yOkCntxRexltTdMQVZ0vSuMw (9 tools configured)

## Current Environment Configuration
- **N8N Base URL**: https://n8n.scrapha.com
- **N8N API Key**: Configured and authenticated
- **Webhook URLs**: All 5 specialized workflows configured
- **OpenAI Integration**: Updated assistant with all workflow tools

## Next Steps
- [ ] Test n8n workflow integrations in development environment
- [ ] Deploy updated configuration to production (Railway/Netlify)
- [ ] Update production environment variables with new assistant ID
- [ ] Verify all n8n webhooks are working correctly
- [ ] Test voice commands that trigger workflow automation
