# N8N Workflow Integration

## Overview
Complete integration of n8n workflow automation tools into both the traditional and realtime JARVIS AI assistants. This enables users to trigger complex automation workflows through voice commands or chat interactions.

## Integration Status: ✅ COMPLETE (2025-07-30)

### Specialized N8N Workflow Tools Implemented

#### 1. n8n_tasks_workflow
- **Purpose**: Google Tasks integration for task management
- **Actions**: create, update, delete, list, complete
- **Parameters**: task_description, action, priority, due_date, additional_data
- **Webhook URL**: https://n8n.scrapha.com/webhook/google-tasks-voice-agent
- **Usage**: "Create a task to buy groceries due tomorrow"

#### 2. n8n_financial_workflow
- **Purpose**: Financial transaction processing and budgeting
- **Actions**: income, expense, transfer tracking
- **Parameters**: transaction_type, amount, description, category, account, date
- **Webhook URL**: https://n8n.scrapha.com/webhook/financial-agent
- **Usage**: "Track this $50 expense for lunch in the food category"

#### 3. n8n_email_workflow
- **Purpose**: Advanced email operations and management
- **Actions**: send, organize, search, archive
- **Parameters**: action, to, from, subject, body, priority
- **Webhook URL**: https://n8n.scrapha.com/webhook/email-assistant
- **Usage**: "Send an <NAME_EMAIL> about the meeting"

#### 4. n8n_receipts_workflow
- **Purpose**: Receipt processing, categorization, and expense tracking
- **Actions**: process, categorize, store, search
- **Parameters**: receipt_data, action, vendor, amount, date, category
- **Webhook URL**: https://n8n.scrapha.com/webhook/receipt-sorter
- **Usage**: "Process this receipt from Starbucks for $4.50"

#### 5. n8n_general_workflow
- **Purpose**: General automation tasks and utility functions
- **Parameters**: request_type, data, priority, additional_data
- **Webhook URL**: https://n8n.scrapha.com/webhook/general-assistant
- **Usage**: "Set up a reminder workflow for my daily standup"

## Technical Implementation

### Traditional Assistant (server.js)
- **Tool Functions**: All 5 specialized n8n workflow functions implemented
- **Error Handling**: Comprehensive try-catch with detailed logging
- **Authentication**: Bearer token authentication with N8N_API_KEY
- **Response Format**: Standardized success/error response structure
- **Source Identification**: All requests tagged with 'jarvis-ai-unified'

### Realtime Assistant (realtime-server.js)
- **Agent Specialization**: Each agent has access to relevant n8n tools
- **Financial Agent**: n8n_financial_workflow + weather tools
- **Task Agent**: n8n_tasks_workflow for Google Tasks integration
- **Email Agent**: n8n_email_workflow + send_email tools
- **Receipts Agent**: n8n_receipts_workflow (new agent added)
- **General Agent**: n8n_general_workflow + weather + web search + generic trigger
- **Source Identification**: All requests tagged with 'jarvis-realtime'

### OpenAI Assistant Configuration
- **Assistant ID**: asst_yOkCntxRexltTdMQVZ0vSuMw
- **Total Tools**: 9 tools configured (4 original + 5 n8n specialized)
- **Setup Script**: Updated setup-assistant.js with all tool definitions
- **Tool Descriptions**: Detailed parameter schemas with enums and validation

## Environment Configuration

### Required Environment Variables
```bash
N8N_BASE_URL=https://n8n.scrapha.com
N8N_API_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
N8N_WEBHOOK_URL=https://n8n.scrapha.com/webhook/google-tasks-voice-agent
N8N_FINANCIAL_WEBHOOK_URL=https://n8n.scrapha.com/webhook/financial-agent
N8N_EMAIL_WEBHOOK_URL=https://n8n.scrapha.com/webhook/email-assistant
N8N_RECEIPTS_WEBHOOK_URL=https://n8n.scrapha.com/webhook/receipt-sorter
N8N_GENERAL_WEBHOOK_URL=https://n8n.scrapha.com/webhook/general-assistant
```

### Docker Configuration
- **docker-compose.yml**: Updated with all n8n environment variables
- **Development Profile**: All webhook URLs available in dev containers
- **Production Profile**: Environment variables passed through to production containers

## Agent Handoff System

### Realtime Agent Routing
- **General Agent**: Entry point, can hand off to any specialized agent
- **Financial Agent**: Can hand off to tasks, email, receipts, or back to general
- **Task Agent**: Can hand off to financial, email, receipts, or back to general
- **Email Agent**: Can hand off to financial, tasks, receipts, or back to general
- **Receipts Agent**: Can hand off to financial, tasks, email, or back to general

### Intelligent Routing
- Users can request specific agents: "Hand me off to the financial agent"
- Agents automatically suggest handoffs: "Would you like me to hand you off to the task agent for this?"
- Context preservation across agent handoffs

## Usage Examples

### Voice Commands (Realtime Assistant)
- "Create a high priority task to review the quarterly report due Friday"
- "Track a $25 expense for gas in my transportation budget"
- "Send an email to the team about tomorrow's meeting"
- "Process this receipt from Target for $67.89 in the household category"
- "Set up an automation to remind me about weekly team meetings"

### Chat Commands (Traditional Assistant)
- "I need to add a task to my Google Tasks list"
- "Help me track my expenses for this month"
- "Compose and send an email to my client"
- "Organize this receipt data for tax purposes"
- "Create a workflow for my morning routine"

## Error Handling & Logging

### Comprehensive Error Handling
- **Network Errors**: Timeout and connection failure handling
- **Authentication Errors**: Invalid API key detection and reporting
- **Webhook Errors**: HTTP status code validation and error messages
- **Parameter Validation**: Required field checking and type validation

### Detailed Logging
- **Request Logging**: All n8n webhook calls logged with parameters
- **Response Logging**: Success/failure status and response data logged
- **Error Logging**: Detailed error messages with stack traces
- **Source Tracking**: Requests tagged with source (unified vs realtime)

## Security Considerations

### Authentication
- **Bearer Token**: N8N_API_KEY used for webhook authentication
- **Environment Variables**: Sensitive data stored in environment variables
- **Request Validation**: Input sanitization and parameter validation

### Data Privacy
- **No Data Storage**: Workflow data passed directly to n8n, not stored locally
- **Secure Transmission**: HTTPS for all webhook communications
- **Minimal Logging**: Sensitive data excluded from logs

## Testing & Verification

### Integration Testing
- [x] All 5 workflow tools successfully created
- [x] Traditional assistant tool functions implemented
- [x] Realtime assistant agents configured
- [x] OpenAI assistant updated with new tools
- [x] Environment variables configured
- [x] Setup script creates assistant with all tools

### Deployment Testing
- [ ] Test n8n webhook connectivity in development
- [ ] Verify authentication with n8n API
- [ ] Test all workflow tools through chat interface
- [ ] Test all workflow tools through voice interface
- [ ] Verify agent handoffs work correctly
- [ ] Test error handling for failed webhook calls

## Future Enhancements

### Potential Improvements
- **Workflow Status Tracking**: Monitor n8n workflow execution status
- **Custom Workflow Creation**: Allow users to create custom workflows through JARVIS
- **Workflow Templates**: Pre-built workflow templates for common tasks
- **Advanced Parameters**: Support for more complex workflow parameters
- **Batch Operations**: Support for multiple workflow triggers in sequence

### Monitoring & Analytics
- **Usage Metrics**: Track which workflows are used most frequently
- **Performance Monitoring**: Monitor webhook response times
- **Error Analytics**: Track and analyze workflow failure patterns
- **User Behavior**: Understand how users interact with workflow automation

## Documentation References
- **Technical Context**: memory-bank/techContext.md
- **Progress Log**: memory-bank/progress.md
- **Deployment Guide**: memory-bank/deployment-checklist.md
- **Active Context**: memory-bank/activeContext.md
