# Technology Context

## Full-Stack Architecture
- **Frontend**: React + Vite (Build tool)
- **Backend**: Node.js + Express
- **Database**: Not directly used (OpenAI Assistants API)
- **AI Integration**: OpenAI Assistants API + Realtime API
- **Deployment**: Netlify (frontend) + Railway (backend)

## Frontend Stack
### Framework & Runtime
- **Vite**: Fast build tool and dev server
- **React 18**: Core UI library
- **JavaScript (ES6+)**: Primary programming language
- **Tailwind CSS**: Utility-first CSS framework for styling

### Key Frontend Dependencies
- `react`: Core React library
- `react-dom`: React DOM rendering
- `lucide-react`: Icon library for UI components
- `react-dropzone`: File upload functionality

### Browser APIs Used
- **Web Speech API**: 
  - `webkitSpeechRecognition`: Speech-to-text functionality
  - `speechSynthesis`: Text-to-speech functionality
- **Fetch API**: HTTP requests to backend API
- **DOM APIs**: Standard web APIs for UI interaction
- **File API**: File upload and processing

## Backend & Database
- **Node.js/Express**: Standalone server handling OpenAI Assistant API calls
- **No direct database**: Uses OpenAI Assistants API for persistence

### Key Backend Dependencies
- `openai`: Official OpenAI API client
- `express`: Web framework
- `cors`: Cross-origin resource sharing
- `helmet`: Security middleware
- `express-rate-limit`: API rate limiting
- `dotenv`: Environment variable management
- `axios`: HTTP client for external APIs

## AI Integration
- **OpenAI Assistants API**: GPT-4 powered conversational AI
- **OpenAI Realtime API**: Voice-to-voice conversation capabilities
- **Function Calling**: Tool integration for weather, email, workflows
- **Thread Management**: Persistent conversation contexts
- **Tool System**: Extensible function definitions for external integrations

## Available Tools/Functions
1. **Weather Information**: Location-based weather data
2. **Email Sending**: SMTP integration capabilities
3. **n8n Workflow Triggers**: Automation workflow integration
4. **File Processing**: Document analysis and categorization
5. **Web Search**: Information retrieval capabilities

### Specialized n8n Workflow Tools
6. **n8n Tasks Workflow**: Google Tasks integration for task management (create, update, delete, list, complete tasks)
7. **n8n Financial Workflow**: Financial transaction processing and budgeting automation
8. **n8n Email Workflow**: Advanced email operations (send, organize, search, archive)
9. **n8n Receipts Workflow**: Receipt processing, categorization, and expense tracking
10. **n8n General Workflow**: General automation tasks and utility functions

## API Architecture
- **RESTful Design**: Standard HTTP methods and status codes
- **JSON Communication**: Request/response format
- **Thread-based Sessions**: Persistent conversation state
- **Error Handling**: Comprehensive error responses
- **Real-time Communication**: WebSocket for voice features

## Security Features
- **Rate Limiting**: API abuse prevention
- **CORS Configuration**: Cross-origin request management
- **Helmet Security**: HTTP header security
- **Environment Variables**: Secure API key management
- **Input Validation**: Request sanitization

## Development & Deployment
- **Package Manager**: pnpm (Standardized for monorepo)
- **Build Tool**: Vite (frontend) + Node.js (backend)
- **Development Server**: Vite dev server (frontend)
- **Local Development**: Docker containers with hot reload
- **Frontend Deployment**: Netlify (production)
- **Backend Deployment**: Railway (production)
- **Live URL**: https://jarvis-frontend.windsurf.build

## Local Development Authentication
- **Mock Authentication**: JWT-based mock auth for development testing
- **Providers**: Google, GitHub, Microsoft mock providers available
- **Endpoints**: `/auth/mock/login`, `/auth/me`, `/auth/logout`
- **Token Storage**: localStorage with automatic validation
- **Configuration**: Separate auth routes for development vs production

## Common Issues & Solutions

### Authentication 404 Errors
**Problem**: Frontend getting 404 errors on auth endpoints
**Cause**: API URL misconfiguration between frontend and backend
**Solution**:
- Check `.env.local` points to correct backend URL
- Ensure auth routes don't include `/api` prefix
- Verify mock auth routes are mounted when OAuth not configured

### Frontend-Backend Connection Issues
**Problem**: Frontend can't connect to backend
**Cause**: Environment variable mismatch or CORS issues
**Solution**:
- Verify `VITE_API_BASE_URL` in `.env.local`
- Check backend CORS configuration
- Ensure both frontend (5173) and backend (5050) are running

### Docker Container Issues
**Problem**: Containers not starting or connecting
**Cause**: Port conflicts, environment variables, or build issues
**Solution**:
- Check `docker-compose ps` for container status
- Verify environment variables in `.env` file
- Check logs with `docker-compose logs -f jarvis-dev`
